<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防汛防台 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    <link rel="stylesheet" href="alert-styles.css">
    <link rel="stylesheet" href="css/flood-prevention.css">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="top-nav">
            <div class="system-title">广西交通运输应急管理系统</div>
            <nav class="tab-navigation">
                <button class="tab-button" data-page="risk-map" onclick="navigateToPage('risk-map.html')">风险一张图</button>
                <button class="tab-button" data-page="emergency-map" onclick="navigateToPage('emergency-map.html')">应急一张图</button>
                <button class="tab-button" data-page="road-network" onclick="navigateToPage('road-network.html')">路网运行</button>
                <button class="tab-button active" data-page="flood-prevention" onclick="navigateToPage('flood-prevention.html')">防汛防台</button>
                <button class="tab-button" data-page="emergency-drill" onclick="navigateToPage('emergency-drill.html')">应急演练</button>
            </nav>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 防汛防台内容 -->
                <div id="flood-prevention-content" class="tab-content flood-prevention-container" style="display: flex;">
            <!-- 左侧资源目录 -->
            <aside class="left-sidebar">
                <div class="resource-filter">
                    <h3>资源目录</h3>
                    <div class="resource-filter-container">
                        <!-- 1. 资源类型选择器 -->
                        <div class="resource-type-selector">
                            <h4>资源类型</h4>
                            <!-- 筛选面板 -->
                            <div class="filter-panel">
                                <!-- 全选复选框 -->
                                <div class="filter-row">
                                    <div class="filter-item checkbox-item">
                                        <input type="checkbox" id="flood-all" name="resource-type" value="all" checked>
                                        <label for="flood-all">全选</label>
                                    </div>
                                </div>

                                <!-- 预警等级下拉框 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="warning-level-select">预警等级：</label>
                                        <select id="warning-level-select" class="filter-select">
                                            <option value="all">所有等级</option>
                                            <option value="red">红色</option>
                                            <option value="orange">橙色</option>
                                            <option value="yellow">黄色</option>
                                            <option value="blue">蓝色</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 灾害类型下拉框 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="disaster-type-select">灾害类型：</label>
                                        <select id="disaster-type-select" class="filter-select">
                                            <option value="all">所有类型</option>
                                            <option value="rainstorm">暴雨</option>
                                            <option value="flood">洪水</option>
                                            <option value="typhoon">台风</option>
                                            <option value="thunderstorm">雷暴大风</option>
                                            <option value="landslide">泥石流</option>
                                            <option value="mountain-flood">山洪</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 发布时间筛选 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="publish-date">发布时间：</label>
                                        <input type="date" id="publish-date" class="filter-date">
                                    </div>
                                </div>

                                <!-- 关键字搜索 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="keyword-search">关键字搜索：</label>
                                        <div class="search-box">
                                            <input type="text" id="keyword-search" placeholder="输入关键字...">
                                            <button id="search-btn"><i class="fas fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警信息列表 -->
                <div class="alert-list-container">
                    <h4>告警信息 <i class="fas fa-bell" style="color: #dc3545; margin-left: 5px;"></i></h4>
                    <div class="alert-tabs">
                        <button class="alert-tab-button active" data-tab="weather-alerts" onclick="switchWeatherAlertTab(this, 'weather-alerts')">气象预警</button>
                    </div>

                    <!-- 气象预警内容 -->
                    <div id="alert-weather-alerts-content" class="alert-tab-content active">
                        <ul class="alert-list">
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-20 07:30</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">玉林市发布暴雨红色预警，预计未来3小时降雨量将超过100毫米</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-19 18:45</div>
                                <div class="alert-content">
                                    <span class="alert-level">橙色预警</span>
                                    <span class="alert-text">柳州市发布洪水橙色预警，柳江水位持续上涨</span>
                                </div>
                            </li>
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-19 16:15</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">桂林市发布山洪红色预警，多地出现山体滑坡风险</span>
                                </div>
                            </li>
                            <li class="alert-item low">
                                <div class="alert-time">2024-05-18 21:20</div>
                                <div class="alert-content">
                                    <span class="alert-level">蓝色预警</span>
                                    <span class="alert-text">梧州市发布雷暴大风蓝色预警，局部地区可能出现8-10级大风</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-18 14:30</div>
                                <div class="alert-content">
                                    <span class="alert-level">黄色预警</span>
                                    <span class="alert-text">北海市发布台风黄色预警，沿海地区将有7-9级大风</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </aside>

            <!-- 地图显示区域 -->
            <section class="map-display-area">
                <!-- 最新告警提示框 -->
                <div class="latest-alert-container" id="flood-map-alert">
                    <div class="latest-alert high">
                        <div class="alert-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-title">最新预警 <span class="alert-time">2024-05-20 07:30</span></div>
                            <div class="alert-message">
                                <span class="alert-level">红色预警</span>
                                <span class="alert-text">玉林市发布暴雨红色预警，预计未来3小时降雨量将超过100毫米</span>
                            </div>
                        </div>
                        <div class="alert-actions">
                            <button class="alert-more-btn" onclick="document.getElementById('alert-weather-alerts-content').scrollIntoView({behavior: 'smooth'})">
                                查看更多
                            </button>
                            <button class="alert-close-btn" onclick="document.getElementById('flood-map-alert').style.display='none'">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 管理按钮容器 -->
                <div class="management-button-container">
                    <!-- 气象预警管理按钮 -->
                    <a href="weather_alerts.html" class="management-button">
                        <i class="fas fa-cloud-sun-rain"></i> 气象预警管理
                    </a>
                </div>

                <img src="lib/map_new.png" alt="广西地图" id="flood-map-image">

                <!-- 图例 -->
                <div class="map-legend">
                    <div class="legend-section">
                        <div class="legend-title">灾害类型</div>
                        <div class="legend-items">
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-cloud-showers-heavy" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">暴雨</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-water" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">洪水</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-wind" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">台风</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-bolt" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">雷暴大风</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-mountain" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">泥石流</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-stream" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">山洪</div>
                            </div>
                        </div>
                    </div>

                    <div class="legend-section">
                        <div class="legend-title">预警级别</div>
                        <div class="legend-items">
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(220, 53, 69, 0.8);"></div>
                                <div class="legend-text">红色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(255, 153, 0, 0.8);"></div>
                                <div class="legend-text">橙色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(255, 193, 7, 0.8);"></div>
                                <div class="legend-text">黄色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(13, 110, 253, 0.8);"></div>
                                <div class="legend-text">蓝色预警</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图上的标注点 - 暴雨预警标点 -->
                <div class="map-marker warning-marker" style="top: 25%; left: 20%; background-color: rgba(255, 193, 7, 0.8);" data-type="rainstorm" data-level="yellow">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 30%; left: 35%; background-color: rgba(13, 110, 253, 0.8);" data-type="rainstorm" data-level="blue">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 45%; left: 60%; background-color: rgba(220, 53, 69, 0.8);" data-type="rainstorm" data-level="red">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 55%; left: 75%; background-color: rgba(255, 153, 0, 0.8);" data-type="rainstorm" data-level="orange">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>

                <!-- 洪水预警标点 -->
                <div class="map-marker warning-marker" style="top: 35%; left: 15%; background-color: rgba(255, 193, 7, 0.8);" data-type="flood" data-level="yellow">
                    <i class="fas fa-water"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 50%; left: 40%; background-color: rgba(220, 53, 69, 0.8);" data-type="flood" data-level="red">
                    <i class="fas fa-water"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 65%; left: 70%; background-color: rgba(13, 110, 253, 0.8);" data-type="flood" data-level="blue">
                    <i class="fas fa-water"></i>
                </div>

                <!-- 台风预警标点 -->
                <div class="map-marker warning-marker" style="top: 40%; left: 45%; background-color: rgba(220, 53, 69, 0.8);" data-type="typhoon" data-level="red">
                    <i class="fas fa-wind"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 60%; left: 80%; background-color: rgba(255, 153, 0, 0.8);" data-type="typhoon" data-level="orange">
                    <i class="fas fa-wind"></i>
                </div>

                <!-- 雷暴大风预警标点 -->
                <div class="map-marker warning-marker" style="top: 35%; left: 50%; background-color: rgba(13, 110, 253, 0.8);" data-type="thunderstorm" data-level="blue">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 55%; left: 25%; background-color: rgba(255, 193, 7, 0.8);" data-type="thunderstorm" data-level="yellow">
                    <i class="fas fa-bolt"></i>
                </div>
            </section>

            <!-- 右侧信息面板 -->
            <aside class="right-sidebar">
                <div class="statistics-panel">
                    <h3>统计分析</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <div class="stat-label">红色预警</div>
                            <div class="stat-value" id="red-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">橙色预警</div>
                            <div class="stat-value" id="orange-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">黄色预警</div>
                            <div class="stat-value" id="yellow-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">蓝色预警</div>
                            <div class="stat-value" id="blue-warning-count">5</div>
                        </div>
                    </div>
                </div>
                <div class="risk-details-list">
                    <h3>详情列表</h3>
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>地市</th>
                                <th>预警名称</th>
                                <th>预警级别</th>
                                <th>发布时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>南宁市</td>
                                <td>暴雨预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-15</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>柳州市</td>
                                <td>台风预警</td>
                                <td><span class="warning-level orange">橙色</span></td>
                                <td>2024-05-15</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>桂林市</td>
                                <td>洪水预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-16</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>玉林市</td>
                                <td>泥石流预警</td>
                                <td><span class="warning-level orange">橙色</span></td>
                                <td>2024-05-16</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>钦州市</td>
                                <td>雷暴大风预警</td>
                                <td><span class="warning-level yellow">黄色</span></td>
                                <td>2024-05-17</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 防汛防台专用JavaScript -->
    <script src="js/flood-prevention.js"></script>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('防汛防台页面已加载');

            // 初始化导航高亮
            if (typeof initializeNavigation === 'function') {
                initializeNavigation('flood-prevention');
            }
        });
    </script>
</body>
</html>
