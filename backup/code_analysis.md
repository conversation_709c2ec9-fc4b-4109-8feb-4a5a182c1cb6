# 代码结构分析文档

## 文件基本信息
- 原文件：`new_index.html`
- 总行数：8234行
- 备份文件：`backup/new_index_original.html`

## 五个主要标签页的行数范围

### 1. 风险一张图 (risk-map) ✅ 已分离
- **起始行**：351行 `<div id="risk-map-content" class="tab-content" style="display: flex;">`
- **结束行**：1777行 `</div>` (风险一张图内容结束)
- **总行数**：约1427行
- **主要内容**：
  - 左侧资源筛选面板
  - 风险隐患点和在建项目切换
  - 地图容器和标点
  - 详情模态框
- **分离状态**：✅ 已完成分离到 `risk-map.html`

### 2. 应急一张图 (emergency-map)
- **起始行**：1778行 `<div id="monitoring-warning-content" class="tab-content" style="display: none;">`
- **结束行**：3688行 `</div>` (应急一张图内容结束)
- **总行数**：约1911行
- **主要内容**：
  - 应急救援圈模式
  - 资源类型切换（应急事件、应急物资、救援力量、其他）
  - 筛选条件面板
  - 告警信息列表
- **分离状态**：⏳ 待分离

### 3. 防汛防台 (flood-prevention)
- **起始行**：3689行 `<div id="flood-typhoon-prevention-content" class="tab-content" style="display: none;">`
- **结束行**：4447行 `</div>` (防汛防台内容结束)
- **总行数**：约759行
- **主要内容**：
  - 防汛防台资源筛选
  - 按单位/路段划分
  - 气象预警信息
  - 防汛物资和队伍信息
- **分离状态**：⏳ 待分离

### 4. 路网运行 (road-network)
- **起始行**：4448行 `<div id="road-network-content" class="tab-content" style="display: none;">`
- **结束行**：5370行 `</div>` (路网运行内容结束)
- **总行数**：约923行
- **主要内容**：
  - 路网运行状态监控
  - 交通流量数据
  - 路况信息
  - 实时监控面板
- **分离状态**：⏳ 待分离

### 5. 应急演练 (emergency-drill)
- **起始行**：5371行 `<div id="emergency-drill-content" class="tab-content" style="display: none;">`
- **结束行**：约8000行 `</div>` (应急演练内容结束)
- **总行数**：约2629行
- **主要内容**：
  - 应急演练计划
  - 演练场景设置
  - 参与单位管理
  - 演练效果评估
- **分离状态**：⏳ 待分离

## 项目进度记录

### ✅ 第一阶段：准备工作和文件分析 (已完成)
- ✅ 创建备份目录和文件
- ✅ 分析代码结构和行数范围
- ✅ 记录各标签页的具体内容

### ✅ 第二阶段：提取公共资源 (已完成)
- ✅ 创建 `css/emergency-common.css` (约15KB)
- ✅ 创建 `js/emergency-common.js` (约8KB)
- ✅ 创建 `components/` 目录结构
- ✅ 更新分析文档

### 🔄 第三阶段：分离各标签页内容 (进行中)

#### ✅ 步骤6：分离风险一张图 (已完成)
**完成时间**：2024-12-19

**已完成的工作**：
- ✅ 提取风险一张图HTML内容 (第351-1777行，约1427行)
- ✅ 创建独立的 `risk-map.html` 页面
- ✅ 包含完整的导航栏和页面结构
- ✅ 保留所有风险隐患点和在建项目标注
- ✅ 保留所有筛选功能和交互逻辑
- ✅ 修改登录页面跳转目标 (`login.js`)
- ✅ 引入公共样式和JavaScript文件

**文件大小**：
- `risk-map.html`: 约1500行 (包含完整页面结构)
- 包含60个风险隐患点标注
- 包含35个在建项目点标注

**功能验证**：
- ✅ 导航栏正常显示和高亮
- ✅ 风险隐患点/在建项目切换正常
- ✅ 筛选功能正常工作
- ✅ 告警信息列表正常显示
- ✅ 统计面板和详情列表正常

**下一步**：
- ⏳ 步骤7：分离应急一张图 (emergency-map.html)
- ⏳ 步骤8：分离路网运行 (road-network.html)  
- ⏳ 步骤9：分离防汛防台 (flood-prevention.html)
- ⏳ 步骤10：分离应急演练 (emergency-drill.html)

## 技术架构

### 文件结构
```
emergency-sys-html/
├── login.html (登录页面)
├── risk-map.html (风险一张图 - 主页面) ✅
├── emergency-map.html (应急一张图) ⏳
├── road-network.html (路网运行) ⏳
├── flood-prevention.html (防汛防台) ⏳
├── emergency-drill.html (应急演练) ⏳
├── css/
│   ├── emergency-common.css (公共样式) ✅
│   └── new_style.css (原有样式)
├── js/
│   ├── emergency-common.js (公共脚本) ✅
│   └── login.js (登录脚本) ✅
├── components/ (组件目录) ✅
├── backup/ (备份目录) ✅
│   ├── new_index_original.html
│   ├── new_style_original.css
│   └── code_analysis.md
└── lib/ (资源文件)
```

### 导航流程
```
用户访问流程：
login.html → risk-map.html (默认主页)
           ↓
    通过导航栏切换到其他页面：
    - emergency-map.html
    - road-network.html  
    - flood-prevention.html
    - emergency-drill.html
```

### 公共资源
- **CSS**: `css/emergency-common.css` - 导航栏、布局、公共组件样式
- **JavaScript**: `js/emergency-common.js` - 导航切换、公共工具函数
- **图标**: Font Awesome 6.0.0 (CDN)

## 预期效果
1. **代码可维护性**：每个页面独立，便于维护和修改
2. **加载性能**：按需加载，减少单页面体积
3. **开发效率**：模块化开发，团队协作更高效
4. **用户体验**：保持原有功能，导航更加清晰

## 其他开发中的标签页
- **起始行**：5355行
- **结束行**：5370行
- **内容**：空的占位div，显示开发中提示

## 页面框架部分
- **头部和导航**：1-350行
- **JavaScript代码**：5371-8234行（约2864行）

## 需要提取的公共资源

### CSS样式
- 导航栏样式 (`.top-nav`, `.tab-navigation`, `.tab-button`)
- 基础布局样式 (`.container`, `.main-content`)
- 公共组件样式 (`.modal`, `.alert`, `.button`)
- 左侧边栏样式 (`.left-sidebar`)
- 地图相关样式 (`.map-display-area`, `.map-marker`)

### JavaScript函数
- 标签页切换逻辑 (`switchTab`)
- 开发中提示弹框 (`showDevelopmentModal`, `closeDevelopmentModal`)
- 公共工具函数
- 模态框通用函数

## 外部依赖文件
- `new_style.css` - 主要样式文件
- `tracking-table.css` - 表格样式
- `alert-styles.css` - 告警样式
- `emergency-drill.css` - 应急演练样式
- Font Awesome 图标库

## 分离计划
1. 保留登录页面 `login.html` 不变
2. 删除 `new_index.html`
3. 创建5个独立的完整页面
4. 提取公共资源到 `css/common.css` 和 `js/common.js`
5. 修改登录跳转目标为 `risk-map.html`

---

## 第二阶段完成情况 ✅

### 已创建的公共资源文件

#### 1. CSS公共样式文件
- **文件位置**：`css/emergency-common.css`
- **文件大小**：约15KB
- **包含内容**：
  - 基础样式重置
  - 主容器布局 (`.container`)
  - 顶部导航栏样式 (`.top-nav`, `.system-title`, `.tab-navigation`, `.tab-button`)
  - 主要内容区域 (`.main-content`, `.tab-content`)
  - 左侧边栏样式 (`.left-sidebar`)
  - 地图显示区域 (`.map-display-area`)
  - 地图标点通用样式 (`.map-marker`)
  - 各类标点样式 (风险、项目、应急事件、救援资源、拥堵等)
  - 地图图例样式 (`.map-legend`)
  - 模态框通用样式 (`.modal`, `.modal-content`)
  - 按钮通用样式 (`.btn`)
  - 表单元素通用样式
  - 工具类样式
  - 响应式设计

#### 2. JavaScript公共函数文件
- **文件位置**：`js/emergency-common.js`
- **文件大小**：约20KB
- **包含功能**：
  - 导航栏高亮逻辑 (`initializeNavigation`)
  - 开发中提示弹框 (`showDevelopmentModal`, `closeDevelopmentModal`)
  - 模态框管理 (`showModal`, `hideModal`)
  - 标签页切换 (`switchTab`, `switchFilterTab`, `switchResourceTab`, `switchAlertTab`)
  - 树形结构展开/收起 (`initializeTreeToggle`)
  - 全选/全不选功能 (`toggleAllCheckboxes`)
  - 地图标点显示/隐藏 (`toggleMapMarkers`)
  - 日期时间格式化 (`formatDateTime`)
  - 防抖和节流函数 (`debounce`, `throttle`)
  - 加载状态管理 (`showLoading`, `hideLoading`)
  - 消息提示 (`showMessage`)
  - 公共事件监听初始化

### 目录结构
```
emergency-sys-html/
├── backup/
│   ├── new_index_original.html (574KB)
│   ├── new_style_original.css (82KB)
│   └── code_analysis.md
├── css/
│   ├── emergency-common.css ✅ (新建)
│   ├── common.css (原有)
│   └── ...
├── js/
│   ├── emergency-common.js ✅ (新建)
│   └── ...
├── components/ ✅ (新建目录)
└── ...
```

### 下一步准备
- 第三阶段：分离各标签页内容
- 从 `new_index.html` 中提取五个主要标签页的HTML内容
- 创建独立的完整页面文件 